from google.adk.tools import ToolContext
import httpx
import os


def verify_brand(brand_name: str, tool_context: ToolContext) -> dict:
    """
    Verifies the provided brand name.

    Args:
        brand_name (str): The brand name to validate.
        tool_context (ToolContext): Context object for the current tool execution. Can be used to extract session state or other information.

    Returns:
        dict: A dictionary containing the status of the verification and associated data.
    """
    url = f"{os.environ.get('TMS_BASE_URL')}/v1/ai-chatbot/brands-list"
    try:

        print("----------verify_brand--------")

        user_context = tool_context.state.get("user:context")

        print("----------brand_name--------", brand_name)

        # Prepare data payload
        payload = {
            "brand_name": brand_name,
            "user_context": user_context,
        }
        # Set the headers for content type (optional, but good practice)
        headers = {
            "Content-Type": "application/json",
            "Authorization": "Bearer " + user_context["token"],
            "Accept": "application/json",
        }

        print("----------headers--------", headers)

        # Make the GET request
        response = httpx.post(url, json=payload, headers=headers)

        # Check if the request was successful
        response.raise_for_status()

        # Parse the response JSON
        response_data = response.json()
        print("Response Data:", response_data)

        if response_data["status"] == "BRAND_VERIFIED":
            # add brand name in state
            tool_context.state["user:brand_name"] = response_data["data"][0]
            return {
                "status": "BRAND_VERIFIED",
                "data": [
                    {
                        "type": "markdown",
                        "content": response_data["data"],
                        "htmlElement": "list",
                    }
                ],
            }
        if response_data["status"] == "PARTIAL_MATCH_FOUND":
            return {
                "status": "PARTIAL_MATCH_FOUND_FOR_BRAND",
                "data": [
                    {
                        "type": "markdown",
                        "content": response_data["data"],
                        "htmlElement": "list",
                    }
                ],
            }
        if response_data["status"] == "NO_MATCH":
            return {
                "status": "NO_MATCH_FOUND_FOR_BRAND",
                "data": [
                    {
                        "type": "markdown",
                        "content": response_data["data"],
                        "htmlElement": "list",
                    }
                ],
            }
    except httpx.HTTPStatusError as http_err:
        print(f"HTTP error occurred: {http_err}")
        return {
            "status": "error",
            "error_message": f"HTTP error: {http_err.response.status_code} - {http_err.response.text}",
        }

    except httpx.RequestError as err:
        print(f"Request error occurred: {err}")
        return {"status": "error", "error_message": f"Network error: {str(err)}"}

    except Exception as e:
        print(f"Unexpected error: {e}")
        return {"status": "error", "error_message": f"Unexpected error: {str(e)}"}

def verify_srvc_type(srvc_type_name: str, tool_context: ToolContext) -> dict:
    """
    Verifies the provided service type.

    Args:
        srvc_type_name (str): The service type to validate.
        tool_context (ToolContext): Context object for the current tool execution. Can be used to extract session state or other information.

    Returns:
        dict: A dictionary containing the status of the verification and associated data.
              The 'status' key will indicate the result, and 'data' will contain relevant information.

        Possible 'status' values and their 'data' structures:
        - "SRVC_TYPE_VERIFIED":
            The service type was successfully verified and found.
        - "PARTIAL_MATCH_FOUND":
            One or more similar service types were found.
        - "NO_MATCH":
            No service type matching the provided name was found.
    """
    url = f"{os.environ.get('TMS_BASE_URL')}/v1/ai-chatbot/srvc-type-list"
    try:

        print("----------verify_srvc_type--------")

        user_context = tool_context.state.get("user:context")
        verified_brand_name = tool_context.state.get("user:brand_name")

        if verified_brand_name is None:
            return {
                "status": "error",
                "error_message": "Brand name not found in session state",
            }

        print(
            "----------user_context-------- verify_srvc_type--------",
            srvc_type_name,
            verified_brand_name,
        )

        print("----------verified_brand_name--------", verified_brand_name)

        # Prepare data payload
        payload = {
            "brand_name": verified_brand_name,
            "srvc_type_name": srvc_type_name,
            "user_context": user_context,
        }
        # Set the headers for content type (optional, but good practice)
        headers = {
            "Content-Type": "application/json",
            "Authorization": "Bearer " + user_context["token"],
            "Accept": "application/json",
        }

        print("----------headers--------", headers)

        # Make the GET request
        response = httpx.post(url, json=payload, headers=headers)

        # Check if the request was successful
        response.raise_for_status()

        # Parse the response JSON
        response_data = response.json()

        if response_data["status"] == "SRVC_TYPE_VERIFIED":
            # add srvc_type_name in state
            tool_context.state["user:srvc_type_name"] = response_data["data"][0]
            return {
                "status": "SRVC_TYPE_VERIFIED",
                "data": [
                    {
                        "type": "markdown",
                        "content": response_data["data"],
                        "htmlElement": "list",
                    }
                ],
            }
        if response_data["status"] == "PARTIAL_MATCH_FOUND":
            return {
                "status": "PARTIAL_MATCH_FOUND_FOR_SRVC_TYPE",
                "data": [
                    {
                        "type": "markdown",
                        "content": response_data["data"],
                        "htmlElement": "list",
                    }
                ],
            }
        if response_data["status"] == "NO_MATCH":
            return {
                "status": "NO_MATCH_FOUND_FOR_SRVC_TYPE",
                "data": [
                    {
                        "type": "markdown",
                        "content": response_data["data"],
                        "htmlElement": "list",
                    }
                ],
            }

    except httpx.HTTPStatusError as http_err:
        print(f"HTTP error occurred: {http_err}")
        return {
            "status": "error",
            "error_message": f"HTTP error: {http_err.response.status_code} - {http_err.response.text}",
        }

    except httpx.RequestError as err:
        print(f"Request error occurred: {err}")
        return {"status": "error", "error_message": f"Network error: {str(err)}"}

    except Exception as e:
        print(f"Unexpected error: {e}")
        return {"status": "error", "error_message": f"Unexpected error: {str(e)}"}

def verify_new_status(status_name: str, tool_context: ToolContext) -> dict:
    """
    Verifies the provided new status.

    Args:
        status_name (str): The new status to validate.
        tool_context (ToolContext): Context object for the current tool execution. Can be used to extract session state or other information.

    Returns:
        dict: A dictionary containing the status of the verification and associated data.
              The 'status' key will indicate the result, and 'data' will contain relevant information.

        Possible 'status' values and their 'data' structures:
        - "STATUS_VERIFIED":
            The new status was successfully verified and found.
        - "PARTIAL_MATCH_FOUND":
            One or more similar new statuses were found.
        - "NO_MATCH":
            No new status matching the provided name was found.
    """
    url = f"{os.environ.get('TMS_BASE_URL')}/v1/ai-chatbot/srvc-type-statuses"
    try:

        print("----------verify_new_status--------")

        user_context = tool_context.state.get("user:context")
        verified_brand_name = tool_context.state.get("user:brand_name")
        verified_srvc_type_name = tool_context.state.get("user:srvc_type_name")

        if verified_brand_name is None:
            return {
                "status": "error",
                "error_message": "Brand name not found in session state",
            }
        if verified_srvc_type_name is None:
            return {
                "status": "error",
                "error_message": "Service type name not found in session state",
            }

        print(
            "----------user_context-------- verify_new_status--------",
            user_context["token"],
            status_name,
            verified_brand_name,
            verified_srvc_type_name,
        )

        # Prepare data payload
        payload = {
            "brand_name": verified_brand_name,
            "srvc_type_name": verified_srvc_type_name,
            "status_name": status_name,
            "user_context": user_context,
        }
        # Set the headers for content type (optional, but good practice)
        headers = {
            "Content-Type": "application/json",
            "Authorization": "Bearer " + user_context["token"],
            "Accept": "application/json",
        }

        print("----------headers--------", headers)

        # Make the GET request
        response = httpx.post(url, json=payload, headers=headers)

        # Check if the request was successful
        response.raise_for_status()

        # Parse the response JSON
        response_data = response.json()

        if response_data["status"] == "STATUS_VERIFIED":
            # add new_status in state
            tool_context.state["user:new_status"] = response_data["data"][0]
            return {
                "status": "STATUS_VERIFIED",
                "data": [
                    {
                        "type": "markdown",
                        "content": response_data["data"],
                        "htmlElement": "list",
                    }
                ],
            }
        if response_data["status"] == "PARTIAL_MATCH_FOUND":
            return {
                "status": "PARTIAL_MATCH_FOUND_FOR_NEW_STATUS",
                "data": [
                    {
                        "type": "markdown",
                        "content": response_data["data"],
                        "htmlElement": "list",
                    }
                ],
            }
        if response_data["status"] == "NO_MATCH":
            return {
                "status": "NO_MATCH_FOUND_FOR_NEW_STATUS",
                "data": [
                    {
                        "type": "markdown",
                        "content": response_data["data"],
                        "htmlElement": "list",
                    }
                ],
            }

    except httpx.HTTPStatusError as http_err:
        print(f"HTTP error occurred: {http_err}")
        return {
            "status": "error",
            "error_message": f"HTTP error: {http_err.response.status_code} - {http_err.response.text}",
        }

    except httpx.RequestError as err:
        print(f"Request error occurred: {err}")
        return {"status": "error", "error_message": f"Network error: {str(err)}"}

    except Exception as e:
        print(f"Unexpected error: {e}")
        return {"status": "error", "error_message": f"Unexpected error: {str(e)}"}

def verify_current_status(
    status_name: str, tool_context: ToolContext
) -> dict:
    """
    Verifies the provided current status.

    Args:
        status_name (str): The current status to validate.
        tool_context (ToolContext): Context object for the current tool execution. Can be used to extract session state or other information.

    Returns:
        dict: A dictionary containing the status of the verification and associated data.
              The 'status' key will indicate the result, and 'data' will contain relevant information.

        Possible 'status' values and their 'data' structures:
        - "STATUS_VERIFIED":
            The current status was successfully verified and found.
        - "PARTIAL_MATCH_FOUND":
            One or more similar current statuses were found.
        - "NO_MATCH":
            No current status matching the provided name was found.
    """
    url = f"{os.environ.get('TMS_BASE_URL')}/v1/ai-chatbot/srvc-type-statuses"
    try:

        print("----------verify_current_status--------")

        user_context = tool_context.state.get("user:context")
        verified_brand_name = tool_context.state.get("user:brand_name")
        verified_srvc_type_name = tool_context.state.get("user:srvc_type_name")

        if verified_brand_name is None:
            return {
                "status": "error",
                "error_message": "Brand name not found in session state",
            }
        if verified_srvc_type_name is None:
            return {
                "status": "error",
                "error_message": "Service type name not found in session state",
            }

        print(
            "----------user_context-------- verify_current_status--------",
            status_name,
            verified_brand_name,
            verified_srvc_type_name,
        )

        # Prepare data payload
        payload = {
            "brand_name": verified_brand_name,
            "srvc_type_name": verified_srvc_type_name,
            "status_name": status_name,
            "user_context": user_context,
        }
        # Set the headers for content type (optional, but good practice)
        headers = {
            "Content-Type": "application/json",
            "Authorization": "Bearer " + user_context["token"],
            "Accept": "application/json",
        }

        print("----------headers--------", headers)

        # Make the GET request
        response = httpx.post(url, json=payload, headers=headers)

        # Check if the request was successful
        response.raise_for_status()

        # Parse the response JSON
        response_data = response.json()

        if response_data["status"] == "STATUS_VERIFIED":
            # add current_status in state
            tool_context.state["user:current_status"] = response_data["data"][0]
            return {
                "status": "STATUS_VERIFIED",
                "data": [
                    {
                        "type": "markdown",
                        "content": response_data["data"],
                        "htmlElement": "list",
                    }
                ],
            }
        if response_data["status"] == "PARTIAL_MATCH_FOUND":
            return {
                "status": "PARTIAL_MATCH_FOUND_FOR_CURRENT_STATUS",
                "data": [
                    {
                        "type": "markdown",
                        "content": response_data["data"],
                        "htmlElement": "list",
                    }
                ],
            }
        if response_data["status"] == "NO_MATCH":
            return {
                "status": "NO_MATCH_FOUND_FOR_CURRENT_STATUS",
                "data": [
                    {
                        "type": "markdown",
                        "content": response_data["data"],
                        "htmlElement": "list",
                    }
                ],
            }

    except httpx.HTTPStatusError as http_err:
        print(f"HTTP error occurred: {http_err}")
        return {
            "status": "error",
            "error_message": f"HTTP error: {http_err.response.status_code} - {http_err.response.text}",
        }

    except httpx.RequestError as err:
        print(f"Request error occurred: {err}")
        return {"status": "error", "error_message": f"Network error: {str(err)}"}

    except Exception as e:
        print(f"Unexpected error: {e}")
        return {"status": "error", "error_message": f"Unexpected error: {str(e)}"}