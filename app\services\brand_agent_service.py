import uuid
from datetime import datetime, timezone
from typing import Optional
from fastapi import <PERSON><PERSON><PERSON><PERSON>x<PERSON>, Form
import google.genai.types as types
from google.adk.runners import Runner
from google.adk.sessions.in_memory_session_service import InMemorySessionService
from google.adk.artifacts import InMemoryArtifactService
from app.agents.root_agent_brand import agent
from app.models.agent_model import UserContext


# Initialize services
session_service = InMemorySessionService()
artifact_service = InMemoryArtifactService()


async def chat_with_brand_agent_logic(
    prompt: str = Form(...),
    user_context: Optional[UserContext] = Form(None),  # Make user_context optional
    is_new_chat: bool = Form(False),
    session_id: Optional[str] = Form(None),  # From frontend on next request
):
    context = {}

    if not prompt:
        raise HTTPException(status_code=400, detail="Prompt is required")

    # Setup runner and agent interaction
    runner, user_id, session_id = await setup_runner(
        user_context, is_new_chat, session_id
    )

    content = types.Content(role="user", parts=[types.Part(text=prompt)])

    final_response_text = "Agent did not produce a final response."

    async for event in runner.run_async(
        user_id=user_id, session_id=session_id, new_message=content
    ):
        print("brand_agent_service.py :: chat_with_brand_agent_logic :: event", event)
        if event.is_final_response():
            if event.content and event.content.parts:
                final_response_text = event.content.parts[0].text
            break

    return final_response_text, context, session_id


async def setup_runner(
    user_context: Optional[UserContext] = None,
    is_new_chat: bool = False,
    session_id: Optional[str] = None,  # From frontend on next request
):
    print("-------------user_context from sp runner--------------", user_context)

    APP_NAME = "tms_brand_agent"
    USER_ID = None
    SESSION_ID = None
    user_context_state = {}

    # Step 1: Generate USER_ID
    if not user_context:
        print("brand_agent_service.py :: setup_runner :: generating new USER_ID")
        USER_ID = str(uuid.uuid4())
    else:
        USER_ID = str(user_context.uuid)
        user_context_state = {
            "user:name": user_context.user_details.name,
            "user:brand_name": user_context.user_details.org.nickname,  # brand name is always the org nickname
            "user:context": {
                "token": user_context.token,
                "uuid": user_context.uuid,
                "hash": user_context.hash,
                "user_details": {
                    "org": {
                        "id": user_context.user_details.org.id,
                        "name_legal": user_context.user_details.org.name_legal,
                        "nickname": user_context.user_details.org.nickname,
                        "org_type": user_context.user_details.org.org_type,
                    }
                },
            },
            "user:org_type": user_context.user_details.org.org_type,
        }

    # Step 2: Determine SESSION_ID
    if is_new_chat:
        print("-------------is_new_chat--------------", is_new_chat)
        timestamp = datetime.now(timezone.utc).strftime("%Y%m%dT%H%M%S")
        SESSION_ID = f"{USER_ID}__{timestamp}"
        print(f"New chat detected. Generated SESSION_ID: {SESSION_ID}")
    else:
        print("-------------session_id--------------", session_id)
        if session_id:
            SESSION_ID = session_id
            print(f"Using existing SESSION_ID: {SESSION_ID}")
        else:
            raise ValueError("session_id must be provided if is_new_chat is False")

    # Step 3: Get or create session
    session = await session_service.get_session(
        app_name=APP_NAME, user_id=USER_ID, session_id=SESSION_ID
    )

    print("-------------session--------------", session)

    if session is None:
        print("Creating new session")
        session = await session_service.create_session(
            app_name=APP_NAME,
            user_id=USER_ID,
            session_id=SESSION_ID,
            state=user_context_state,
        )

    # Step 4: Initialize the runner
    runner = Runner(
        agent=agent.root_agent,
        app_name=APP_NAME,
        session_service=session_service,
        artifact_service=artifact_service,
    )

    return runner, USER_ID, SESSION_ID
