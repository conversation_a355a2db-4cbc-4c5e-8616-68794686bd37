aiohappyeyeballs==2.6.1
aiohttp==3.12.13
aiosignal==1.3.2
annotated-types==0.7.0
anyio==4.9.0
asttokens==3.0.0
attrs==25.3.0
Authlib==1.6.0
backcall==0.2.0
beautifulsoup4==4.13.4
bleach==6.2.0
cachetools==5.5.2
certifi==2025.6.15
cffi==1.17.1
charset-normalizer==3.4.2
click==8.2.1
colorama==0.4.6
cryptography==45.0.4
decorator==5.2.1
defusedxml==0.7.1
Deprecated==1.2.18
distro==1.9.0
docopt==0.6.2
docstring_parser==0.16
executing==2.2.0
fastapi==0.115.12
fastjsonschema==2.21.1
filelock==3.18.0
frozenlist==1.7.0
fsspec==2025.5.1
google-adk==1.1.1
google-ai-generativelanguage==0.6.15
google-api-core==2.25.1
google-api-python-client==2.173.0
google-auth==2.40.3
google-auth-httplib2==0.2.0
google-cloud-aiplatform==1.97.0
google-cloud-bigquery==3.34.0
google-cloud-core==2.4.3
google-cloud-resource-manager==1.14.2
google-cloud-secret-manager==2.24.0
google-cloud-speech==2.33.0
google-cloud-storage==2.19.0
google-cloud-trace==1.16.2
google-crc32c==1.7.1
google-genai==1.17.0
google-generativeai==0.8.5
google-resumable-media==2.7.2
googleapis-common-protos==1.70.0
graphviz==0.21
greenlet==3.2.3
grpc-google-iam-v1==0.14.2
grpcio==1.73.0
grpcio-status==1.71.0
gunicorn==23.0.0
h11==0.16.0
httpcore==1.0.9
httplib2==0.22.0
httpx==0.28.1
httpx-sse==0.4.0
huggingface-hub==0.33.1
idna==3.10
importlib_metadata==8.7.0
ipython==8.12.3
jedi==0.19.2
Jinja2==3.1.6
jiter==0.10.0
jsonschema==4.24.0
jsonschema-specifications==2025.4.1
jupyter_client==8.6.3
jupyter_core==5.8.1
jupyterlab_pygments==0.3.0
litellm==1.73.2
MarkupSafe==3.0.2
matplotlib-inline==0.1.7
mcp==1.9.4
mistune==3.1.3
multidict==6.5.1
nbclient==0.10.2
nbconvert==7.16.6
nbformat==5.10.4
numpy==2.3.0
openai==1.88.0
opentelemetry-api==1.34.1
opentelemetry-exporter-gcp-trace==1.9.0
opentelemetry-resourcedetector-gcp==1.9.0a0
opentelemetry-sdk==1.34.1
opentelemetry-semantic-conventions==0.55b1
packaging==25.0
pandocfilters==1.5.1
parso==0.8.4
pickleshare==0.7.5
pipreqs==0.5.0
platformdirs==4.3.8
prompt_toolkit==3.0.51
propcache==0.3.2
proto-plus==1.26.1
protobuf==5.29.5
pure_eval==0.2.3
pyasn1==0.6.1
pyasn1_modules==0.4.2
pycparser==2.22
pydantic==2.11.7
pydantic-settings==2.9.1
pydantic_core==2.33.2
Pygments==2.19.1
pyparsing==3.2.3
python-dateutil==2.9.0.post0
python-dotenv==1.1.0
python-multipart==0.0.20
pywin32==310
PyYAML==6.0.2
pyzmq==27.0.0
referencing==0.36.2
regex==2024.11.6
requests==2.32.4
rpds-py==0.25.1
rsa==4.9.1
shapely==2.1.1
six==1.17.0
sniffio==1.3.1
soupsieve==2.7
SQLAlchemy==2.0.41
sse-starlette==2.3.6
stack-data==0.6.3
starlette==0.46.2
tiktoken==0.9.0
tinycss2==1.4.0
tokenizers==0.21.2
tornado==6.5.1
tqdm==4.67.1
traitlets==5.14.3
typing-inspection==0.4.1
typing_extensions==4.14.0
tzdata==2025.2
tzlocal==5.3.1
uritemplate==4.2.0
urllib3==2.5.0
uvicorn==0.34.2
wcwidth==0.2.13
webencodings==0.5.1
websockets==15.0.1
wrapt==1.17.2
yarg==0.1.9
yarl==1.20.1
zipp==3.23.0
