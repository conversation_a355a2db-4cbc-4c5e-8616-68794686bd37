from typing import List, Any
from app.libs.openai import openai_models,openai_client


def convert_to_markdown(
    html_element: str, content: List[Any], remaining_count: int = 0
) -> str:
    """
    Uses OpenAI to convert the given content into markdown format based on the html_element type.
    Ensures the AI outputs ONLY the markdown, with no extra text or code fences.

    Args:
        html_element (str): The type of HTML element (e.g., 'list', 'table', 'text', etc.).
                            This guides the AI on the desired markdown structure.
        content (List[Any]): The content to be converted into markdown.
                             For 'list', this is typically a list of strings.
                             For 'table', this might be a list of lists (rows) or list of dictionaries.
                             The function converts this into a string format for the LLM.

    Returns:
        str: The converted markdown content, stripped of any extra whitespace.
    """
    # Convert content into a string representation for the LLM.
    # For lists, joining by newline is effective.
    # For tables, depending on the 'content' structure, you might need a more
    # sophisticated conversion (e.g., to CSV-like string or JSON string)
    # so the LLM can correctly infer columns/rows.
    content_text = "\n".join(str(item) for item in content)

    # --- Constructing the prompt with strict instructions ---
    # The prompt explicitly tells the model to output ONLY the markdown.
    base_instruction = ""
    if html_element == "list":
        base_instruction = "Convert the following items into a markdown ordered list."
        if (
            remaining_count > 0
        ):  # Add the "more entries" line break and text if remaining_count > 0
            base_instruction += f"\n\nIncluding any additional information such as '{remaining_count}' more requests...."
    elif html_element == "table":
        # For tables, the LLM performs best if the input data structure is clear.
        # This example assumes 'content_text' adequately represents table data for the LLM.
        # You might need to refine 'content_text' generation for complex table data.
        # For tables, conditionally include the 'remaining_count' if it is present
        base_instruction = f"Convert the following data into a markdown table."

        if (
            remaining_count > 0
        ):  # Add the "more entries" line break and text if remaining_count > 0
            base_instruction += f"\n\nIncluding any additional information such as '{remaining_count}' more requests...."
    elif html_element == "text" or html_element == "paragraph":
        print("helpers.py :: convert_to_markdown :: text content", content_text)
        base_instruction = "Format the following text as a markdown paragraph."
    else:
        # Generic instruction for other html_element types
        base_instruction = f"Convert the following content into markdown formatted as a {html_element}."

    prompt = (
        f"{base_instruction}\n"
        f"**Output ONLY the markdown, with no introductory or concluding text, "
        f"no conversational phrases, and no code block fences.**\n\n"
        f"Content to convert:\n{content_text}"
    )

    messages = [{"role": "user", "content": prompt}]

    # --- Call OpenAI API to generate markdown content ---
    try:
        response = openai_client.chat.completions.create(
            model=openai_models["gpt4o"],  # Specify the gpt-4o model
            messages=messages,
            temperature=0.0,  # Set to 0.0 for deterministic, less creative, and more concise output
            max_tokens=2000,  # Adjust max_tokens based on your expected maximum markdown output size
        )

        # Access the markdown content and strip any leading/trailing whitespace or newlines
        markdown_content = response.choices[0].message.content.strip()
        print("helpers.py :: convert_to_markdown :: markdown_content", markdown_content)
        return markdown_content

    except Exception as e:
        print(f"Error calling OpenAI API for markdown conversion: {e}")
        # Depending on your error handling strategy, you might re-raise,
        # return an empty string, or a default error message.
        return f"Error: Could not convert content to markdown. {e}"
