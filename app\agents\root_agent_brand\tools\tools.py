import os
import json
from typing import List, Dict, Any, Optional
from copy import deepcopy
import httpx
from google.genai import types
from google.adk.tools import ToolContext
from google.adk.tools.base_tool import BaseTool
from google.adk.agents.callback_context import CallbackContext
from google.adk.models import LlmResponse
from app.utils.helpers import convert_to_markdown
from app.utils.tms.verification import verify_srvc_type, verify_current_status, verify_new_status


def simple_after_model_modifier(
    callback_context: CallbackContext, llm_response: LlmResponse
) -> Optional[LlmResponse]:
    """
    Inspects/modifies the LLM response after it's received.
    Converts the plain text LLM response into a structured JSON array string,
    handling cases where the LLM might already output the target JSON structure.
    """
    agent_name = callback_context.agent_name
    print("tools.py :: simple_after_model_modifier :: agent_name", agent_name)
    print("tools.py :: simple_after_model_modifier :: original LlmResponse", llm_response)

    original_text = ""
    # Ensure there's content and it's not a function call or error
    if llm_response.content and llm_response.content.parts:
        if llm_response.content.parts[0].text:
            original_text = llm_response.content.parts[0].text
            print("tools.py :: simple_after_model_modifier :: original response text", original_text)
        elif llm_response.content.parts[0].function_call:
            print(
                "tools.py :: simple_after_model_modifier :: function call", llm_response.content.parts[0].function_call.name
            )
            return None
        else:
            print("tools.py :: simple_after_model_modifier :: no text content found in first part")
            return None
    elif llm_response.error_message:
        print(
            "tools.py :: simple_after_model_modifier :: error message", llm_response.error_message
        )

        # Determine if it's a tool error (common message patterns) or a general LLM error
        error_msg_lower = llm_response.error_message.lower()
        if any(
            keyword in error_msg_lower
            for keyword in ["tool", "function", "http", "timeout", "network"]
        ):
            error_title = "⚙️ **Tool Error**"
        else:
            error_title = "🤖 **AI Response Error**"

        # Format the markdown content
        error_markdown = f"{error_title}\n\n{llm_response.error_message}"

        # Structure it as markdown JSON
        error_json_array_data: List[Dict[str, Any]] = [
            {
                "type": "markdown",
                "content": error_markdown,
            }
        ]

        json_string_output = json.dumps(error_json_array_data, indent=2)
        modified_parts = [types.Part(text=json_string_output)]
        new_response_content = types.Content(role="model", parts=modified_parts)

        new_response = LlmResponse(
            content=new_response_content,
            grounding_metadata=llm_response.grounding_metadata,
        )

        print("tools.py :: simple_after_model_modifier :: returning formatted error response")
        return new_response
    else:
        print("tools.py :: simple_after_model_modifier :: empty LlmResponse or no content parts")
        return None

    # --- NEW LOGIC START ---
    # Determine the actual content that should go into the 'content' field of the final JSON
    final_content_for_json_array = original_text

    try:
        # Attempt to parse the original_text to see if it's already a JSON structure
        temp_parsed_content = json.loads(original_text)

        print("tools.py :: simple_after_model_modifier :: temp parsed content", temp_parsed_content)
        # Check if it matches the expected top-level structure:
        # A list containing a single dictionary with 'type' and 'content' keys.
        if (
            isinstance(temp_parsed_content, list)
            and len(temp_parsed_content) == 1
            and isinstance(temp_parsed_content[0], dict)
            and "type" in temp_parsed_content[0]
            and "content" in temp_parsed_content[0]
        ):

            # If it is, extract the inner 'content' string
            final_content_for_json_array = temp_parsed_content[0]["content"]
            print(
                "tools.py :: simple_after_model_modifier :: extracted inner content from target JSON structure"
            )
        else:
            # It's a valid JSON, but not the specific structure we're looking for,
            # so treat the original_text as raw markdown.
            print(
                "tools.py :: simple_after_model_modifier :: valid JSON but not target structure, treating as raw markdown"
            )
    except json.JSONDecodeError:
        # If original_text is not a valid JSON string at all, treat it as raw markdown content.
        print(
            "tools.py :: simple_after_model_modifier :: not valid JSON string, treating as raw markdown"
        )
        pass  # final_content_for_json_array remains original_text

    # --- Modification: Convert text to desired JSON array format ---
    print("tools.py :: simple_after_model_modifier :: preparing response for JSON array format")

    # Create the Python list of dictionaries using the determined content
    text_as_json_array_data: List[Dict[str, Any]] = [
        {
            "type": "markdown",
            "content": final_content_for_json_array,  # Use the potentially extracted content
        },
    ]

    # Convert the Python data structure into a JSON string
    json_string_output = json.dumps(text_as_json_array_data, indent=2)

    # Create a NEW LlmResponse with the modified content
    modified_parts = [types.Part(text=json_string_output)]

    new_response_content = types.Content(role="model", parts=modified_parts)

    new_response = LlmResponse(
        content=new_response_content, grounding_metadata=llm_response.grounding_metadata
    )

    print("tools.py :: simple_after_model_modifier :: returning modified response with structured JSON content")
    print(
        "tools.py :: simple_after_model_modifier :: new LlmResponse content text", new_response.content.parts[0].text
    )

    return new_response


def simple_after_tool_modifier(
    tool: BaseTool, args: Dict[str, Any], tool_context: ToolContext, tool_response: Dict
) -> Optional[Dict]:
    """Inspects/modifies the tool result after execution."""
    agent_name = tool_context.agent_name
    tool_name = tool.name
    print("tools.py :: simple_after_tool_modifier :: tool call", f"tool '{tool_name}' in agent '{agent_name}'")
    print("tools.py :: simple_after_tool_modifier :: args used", args)
    print("tools.py :: simple_after_tool_modifier :: original tool_response", tool_response)

    if tool_response["status"] == "error":
        print("tools.py :: simple_after_tool_modifier :: tool error", tool_response['error_message'])
        return None

    if tool_response["data"][0]["type"] == "markdown":
        content = tool_response["data"][0]["content"]
        print("tools.py :: simple_after_tool_modifier :: content", content)
        # Convert the content to markdown
        remaining_count = tool_response["data"][0].get("remaining_count", 0)
        markdown_content = convert_to_markdown(
            tool_response["data"][0]["htmlElement"], content, remaining_count
        )
        print("tools.py :: simple_after_tool_modifier :: markdown content", markdown_content)

        modified_tool_response = deepcopy(tool_response)
        modified_tool_response["data"][0]["content"] = markdown_content
        modified_tool_response["data"][0].pop("htmlElement")

        print("tools.py :: simple_after_tool_modifier :: modified tool_response", modified_tool_response)

        return modified_tool_response

    # Return None to use the original tool_response
    return None


def list_available_srvc_types(tool_context: ToolContext) -> dict:
    """
     Lists all available service types for the brand.

    Args:
        tool_context (ToolContext): Context object for the current tool execution. Can be used to extract session state or other information.

    Returns:
         dict: A dictionary containing the status of the request and the data.
               The 'data' field will be an array of one object. This object will have:
               - 'type': "markdown"
               - 'content': list of service types returned from backend.
               - 'htmlElement': An HTML element type (e.g., "list") indicating how the content should be rendered.
    """
    url = f"{os.environ.get('TMS_BASE_URL')}/v1/ai-chatbot/srvc-type-list"
    try:

        print("tools.py :: list_available_srvc_types :: function start")

        user_context = tool_context.state.get("user:context")
        srvc_type_name = ""
        verified_brand_name = tool_context.state.get("user:brand_name")

        if verified_brand_name is None:
            return {
                "status": "error",
                "error_message": "Brand name not found in session state",
            }

        print(
            "tools.py :: list_available_srvc_types :: user_context and brand_name",
            user_context["token"],
            verified_brand_name,
        )
        print("tools.py :: list_available_srvc_types :: srvc_type_name", srvc_type_name)
        print("tools.py :: list_available_srvc_types :: verified_brand_name", verified_brand_name)

        # Prepare data payload
        payload = {
            "brand_name": verified_brand_name,
            "srvc_type_name": srvc_type_name,
            "user_context": user_context,
        }
        # Set the headers for content type (optional, but good practice)
        headers = {
            "Content-Type": "application/json",
            "Authorization": "Bearer " + user_context["token"],
            "Accept": "application/json",
        }

        print("tools.py :: list_available_srvc_types :: headers", headers)

        # Make the GET request
        response = httpx.post(url, json=payload, headers=headers)

        # Check if the request was successful
        response.raise_for_status()

        # Parse the response JSON
        response_data = response.json()
        print("tools.py :: list_available_srvc_types :: response data", response_data)

        return {
            "status": "success",
            "data": [
                {
                    "type": "markdown",
                    "content": response_data["data"],
                    "htmlElement": "list",
                },
            ],
        }

    except httpx.HTTPStatusError as http_err:
        print("tools.py :: list_available_srvc_types :: HTTP error", http_err)
        return {
            "status": "error",
            "error_message": f"HTTP error: {http_err.response.status_code} - {http_err.response.text}",
        }

    except httpx.RequestError as err:
        print("tools.py :: list_available_srvc_types :: request error", err)
        return {"status": "error", "error_message": f"Network error: {str(err)}"}

    except Exception as e:
        print("tools.py :: list_available_srvc_types :: unexpected error", e)
        return {"status": "error", "error_message": f"Unexpected error: {str(e)}"}


def list_available_statuses(srvc_type_name: str, tool_context: ToolContext) -> dict:
    """
    Lists all available statuses for a service type.

    Args:
        srvc_type_name (str): The service type for which to list statuses.
        tool_context (ToolContext): Context object for the current tool execution. Can be used to extract session state or other information.

    Returns:
        dict: A dictionary containing the status of the verification and associated data.
    """
    url = f"{os.environ.get('TMS_BASE_URL')}/v1/ai-chatbot/srvc-type-statuses"
    try:

        print("tools.py :: list_available_statuses :: function start")

        user_context = tool_context.state.get("user:context")
        verified_brand_name = tool_context.state.get("user:brand_name")

        if verified_brand_name is None:
            return {
                "status": "error",
                "error_message": "Brand name not found in session state",
            }

        # verify service type name
        call_verify_srvc_type = verify_srvc_type(srvc_type_name, tool_context)
        verified_srvc_type_name = ""

        if call_verify_srvc_type["status"] != "SRVC_TYPE_VERIFIED":
            return call_verify_srvc_type
        else:
            verified_srvc_type_name = call_verify_srvc_type["data"][0]["content"][0]

        print(
            "tools.py :: list_available_statuses :: verified brand and service type",
            verified_brand_name,
            verified_srvc_type_name,
        )

        # Prepare data payload
        payload = {
            "status_name": "",
            "brand_name": verified_brand_name,
            "srvc_type_name": verified_srvc_type_name,
            "user_context": user_context,
        }
        # Set the headers for content type (optional, but good practice)
        headers = {
            "Content-Type": "application/json",
            "Authorization": "Bearer " + user_context["token"],
            "Accept": "application/json",
        }

        print("tools.py :: list_available_statuses :: headers", headers)

        # Make the GET request
        response = httpx.post(url, json=payload, headers=headers)

        # Check if the request was successful
        response.raise_for_status()

        # Parse the response JSON
        response_data = response.json()

        print("tools.py :: list_available_statuses :: response data", response_data)

        return {
            "status": "success",
            "data": [
                {
                    "type": "markdown",
                    "content": response_data["data"],
                    "htmlElement": "list",
                }
            ],
        }

    except httpx.HTTPStatusError as http_err:
        print("tools.py :: list_available_statuses :: HTTP error", http_err)
        return {
            "status": "error",
            "error_message": f"HTTP error: {http_err.response.status_code} - {http_err.response.text}",
        }

    except httpx.RequestError as err:
        print("tools.py :: list_available_statuses :: request error", err)
        return {"status": "error", "error_message": f"Network error: {str(err)}"}

    except Exception as e:
        print("tools.py :: list_available_statuses :: unexpected error", e)
        return {"status": "error", "error_message": f"Unexpected error: {str(e)}"}


def list_srvc_requests_to_update(
    srvc_type_name: str,
    current_status_name: str,
    new_status_name: str,
    tool_context: ToolContext,
) -> dict:
    """
    Lists all service requests that will be updated.

    Args:
        srvc_type_name (str): The service type for which to list service requests.
        current_status_name (str): The current status for which to list service requests.
        new_status_name (str): The new status for which to list service requests.
        tool_context (ToolContext): Context object for the current tool execution. Can be used to extract session state or other information.

    Returns:
         dict: A dictionary containing the status of the request and the data.
               The 'data' field will be an array of one object. This object will have:
               - 'type': "markdown"
               - 'content': list of service requests returned from backend.
               - 'htmlElement': An HTML element type (e.g., "list") indicating how the content should be rendered.
    """
    url = f"{os.environ.get('TMS_BASE_URL')}/v1/ai-chatbot/srvc-reqs"
    timeout = httpx.Timeout(120.0, connect=10.0)
    try:

        print("tools.py :: list_srvc_requests_to_update :: function start")

        user_context = tool_context.state.get("user:context")
        verified_brand_name = tool_context.state.get("user:brand_name")

        if verified_brand_name is None:
            return {
                "status": "error",
                "error_message": "Brand name not found in session state",
            }

        # verify service type name
        call_verify_srvc_type = verify_srvc_type(srvc_type_name, tool_context)
        verified_srvc_type_name = ""

        if call_verify_srvc_type["status"] != "SRVC_TYPE_VERIFIED":
            return call_verify_srvc_type
        else:
            verified_srvc_type_name = call_verify_srvc_type["data"][0]["content"][0]

        # verify current status name
        call_verify_current_status = verify_current_status(
            current_status_name, tool_context
        )
        verified_current_status = ""

        if call_verify_current_status["status"] != "STATUS_VERIFIED":
            return call_verify_current_status
        else:
            verified_current_status = call_verify_current_status["data"][0]["content"][
                0
            ]

        # verify new status name
        call_verify_new_status = verify_new_status(new_status_name, tool_context)
        verified_new_status = ""

        if call_verify_new_status["status"] != "STATUS_VERIFIED":
            return call_verify_new_status
        else:
            verified_new_status = call_verify_new_status["data"][0]["content"][0]

        print(
            "tools.py :: list_srvc_requests_to_update :: verified parameters",
            verified_brand_name,
            verified_srvc_type_name,
            verified_current_status,
            verified_new_status,
        )

        # Prepare data payload
        payload = {
            "brand_name": verified_brand_name,
            "srvc_type_name": verified_srvc_type_name,
            "current_status_name": verified_current_status,
            "user_context": user_context,
        }
        # Set the headers for content type (optional, but good practice)
        headers = {
            "Content-Type": "application/json",
            "Authorization": "Bearer " + user_context["token"],
            "Accept": "application/json",
        }

        print("tools.py :: list_srvc_requests_to_update :: headers", headers)

        # Make the GET request
        response = httpx.post(url, json=payload, headers=headers, timeout=timeout)

        # Check if the request was successful
        response.raise_for_status()

        # Parse the response JSON
        response_data = response.json()

        print("tools.py :: list_srvc_requests_to_update :: response data", response_data)

        if response_data["status"] == "SRVC_REQS_NOT_FOUND":
            return {
                "status": "SRVC_REQS_NOT_FOUND",
                "data": [
                    {
                        "type": "text",
                        "content": "No service requests found.",
                        "htmlElement": "text",
                    }
                ],
            }

        return {
            "status": "success",
            "data": [
                {
                    "type": "markdown",
                    "content": response_data["data"],
                    "remaining_count": response_data["total_count"]
                    - len(response_data["data"]),
                    "htmlElement": "list",
                }
            ],
        }

    except httpx.HTTPStatusError as http_err:
        print("tools.py :: list_srvc_requests_to_update :: HTTP error", http_err)
        return {
            "status": "error",
            "error_message": f"HTTP error: {http_err.response.status_code} - {http_err.response.text}",
        }

    except httpx.RequestError as err:
        print("tools.py :: list_srvc_requests_to_update :: request error", err)
        return {"status": "error", "error_message": f"Network error: {str(err)}"}

    except Exception as e:
        print("tools.py :: list_srvc_requests_to_update :: unexpected error", e)
        return {"status": "error", "error_message": f"Unexpected error: {str(e)}"}


def update_srvc_requests(
    srvc_type_name: str,
    current_status_name: str,
    new_status_name: str,
    tool_context: ToolContext,
) -> dict:
    """
    Updates all service requests that match the criteria.

    Args:
        srvc_type_name (str): The service type for which to update service requests.
        current_status_name (str): The current status for which to update service requests.
        new_status_name (str): The new status for which to update service requests.
        tool_context (ToolContext): Context object for the current tool execution. Can be used to extract session state or other information.

    Returns:
         dict: A dictionary containing the status of the request and the data.
               The 'data' field will be an array of one object. This object will have:
               - 'type': "markdown"
               - 'content': list of service requests returned from backend.
               - 'htmlElement': An HTML element type (e.g., "list") indicating how the content should be rendered.
    """
    url = f"{os.environ.get('TMS_BASE_URL')}/v1/ai-chatbot/srvc-reqs-modify"
    try:

        print("tools.py :: update_srvc_requests :: function start")

        user_context = tool_context.state.get("user:context")
        verified_brand_name = tool_context.state.get("user:brand_name")

        if verified_brand_name is None:
            return {
                "status": "error",
                "error_message": "Brand name not found in session state",
            }

        # verify service type name
        call_verify_srvc_type = verify_srvc_type(srvc_type_name, tool_context)
        verified_srvc_type_name = ""

        if call_verify_srvc_type["status"] != "SRVC_TYPE_VERIFIED":
            return call_verify_srvc_type
        else:
            verified_srvc_type_name = call_verify_srvc_type["data"][0]["content"][0]

        # verify current status name
        call_verify_current_status = verify_current_status(
            current_status_name, tool_context
        )
        verified_current_status = ""

        if call_verify_current_status["status"] != "STATUS_VERIFIED":
            return call_verify_current_status
        else:
            verified_current_status = call_verify_current_status["data"][0]["content"][
                0
            ]

        # verify new status name
        call_verify_new_status = verify_new_status(new_status_name, tool_context)
        verified_new_status = ""

        if call_verify_new_status["status"] != "STATUS_VERIFIED":
            return call_verify_new_status
        else:
            verified_new_status = call_verify_new_status["data"][0]["content"][0]

        print(
            "tools.py :: update_srvc_requests :: verified parameters",
            verified_brand_name,
            verified_srvc_type_name,
            verified_current_status,
            verified_new_status,
        )

        # Prepare data payload
        payload = {
            "brand_name": verified_brand_name,
            "srvc_type_name": verified_srvc_type_name,
            "current_status_name": verified_current_status,
            "new_status_name": verified_new_status,
            "user_context": user_context,
        }
        # Set the headers for content type (optional, but good practice)
        headers = {
            "Content-Type": "application/json",
            "Authorization": "Bearer " + user_context["token"],
            "Accept": "application/json",
        }

        print("tools.py :: update_srvc_requests :: headers", headers)

        # Make the GET request
        response = httpx.post(url, json=payload, headers=headers)

        # Check if the request was successful
        response.raise_for_status()

        # Parse the response JSON
        response_data = response.json()

        print("tools.py :: update_srvc_requests :: response data", response_data)

        # Here also add the total count logic in case failed entries > 50
        if response_data["status"] == "PARTIALLY_UPDATED":
            return {
                "status": "PARTIALLY_UPDATED",
                "data": [
                    {
                        "type": "markdown",
                        "content": response_data["data"],
                        "htmlElement": "list",
                    }
                ],
            }

        if response_data["status"] == "PROCESSED_IN_QUEUE":
            return {
                "status": "PROCESSED_IN_QUEUE",
                "data": [
                    {
                        "type": "text",
                        "content": f"{response_data['total_count']} service requests are being processed. You will receive a notification once the processing is complete.",
                        "htmlElement": "text",
                    }
                ],
            }

        if response_data["status"] == "ALL_UPDATED":
            return {
                "status": "ALL_UPDATED",
                "data": [
                    {
                        "type": "text",
                        "content": f"{len(response_data['data'])} service requests updated successfully.",
                        "htmlElement": "text",
                    }
                ],
            }

        if response_data["status"] == "ALL_FAILED":
            return {
                "status": "ALL_FAILED",
                "data": [
                    {
                        "type": "text",
                        "content": "All service requests failed to update.",
                        "htmlElement": "text",
                    }
                ],
            }

    except httpx.HTTPStatusError as http_err:
        print("tools.py :: update_srvc_requests :: HTTP error", http_err)
        return {
            "status": "error",
            "error_message": f"HTTP error: {http_err.response.status_code} - {http_err.response.text}",
        }

    except httpx.RequestError as err:
        print("tools.py :: update_srvc_requests :: request error", err)
        return {"status": "error", "error_message": f"Network error: {str(err)}"}

    except Exception as e:
        print("tools.py :: update_srvc_requests :: unexpected error", e)
        return {"status": "error", "error_message": f"Unexpected error: {str(e)}"}
